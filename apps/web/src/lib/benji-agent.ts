/**
 * Benji AI Agent - Core Implementation
 * 
 * The main AI agent that orchestrates all tools and generates responses
 */

import { streamText, generateId } from 'ai';
import { getModel, getModelByPlan, type ModelName } from './ai-providers';
import { xaiSearchTool } from './tools/xai-search';
import { exaSearchTool } from './tools/exa-search';
import { imageGenerationTool } from './tools/openai-image';
import { prisma } from './db-utils';

export interface BenjiConfig {
  model?: ModelName;
  userId?: string;
  userPlan?: string;
  maxTokens?: number;
  temperature?: number;
  enableTools?: boolean;
  maxSteps?: number;
  personalityPrompt?: string;
  customSystemPrompt?: string;
}

export interface BenjiContext {
  mentionId?: string;
  mentionContent?: string;
  authorInfo?: {
    name: string;
    handle: string;
    avatarUrl?: string;
  };
  conversationHistory?: Array<{
    role: 'user' | 'assistant';
    content: string;
  }>;
}

export class BenjiAgent {
  private config: BenjiConfig;
  
  constructor(config: BenjiConfig = {}) {
    this.config = {
      model: config.model || 'gemini25Flash',
      userId: config.userId,
      userPlan: config.userPlan || 'reply-guy',
      maxTokens: config.maxTokens || 4000,
      temperature: config.temperature || 0.7,
      enableTools: config.enableTools ?? true,
      maxSteps: config.maxSteps || 5,
      personalityPrompt: config.personalityPrompt,
      customSystemPrompt: config.customSystemPrompt,
    };

    // Auto-select model based on user plan if not specified
    if (!config.model && config.userPlan) {
      this.config.model = getModelByPlan(config.userPlan);
    }
  }

  /**
   * Generate a response for a Twitter mention
   */
  async generateMentionResponse(
    mentionContent: string,
    context: BenjiContext = {}
  ) {
    const systemPrompt = this.buildSystemPrompt('mention', context);
    
    const messages = [
      {
        role: 'system' as const,
        content: systemPrompt,
      },
      {
        role: 'user' as const,
        content: `Please analyze this tweet and generate an appropriate response:\n\n"${mentionContent}"`,
      },
    ];

    return this.streamResponse(messages, context);
  }

  /**
   * Generate a response for any tweet (Quick Reply feature)
   */
  async generateQuickReply(
    tweetContent: string,
    context: BenjiContext = {}
  ) {
    const systemPrompt = this.buildSystemPrompt('quick-reply', context);
    
    const messages = [
      {
        role: 'system' as const,
        content: systemPrompt,
      },
      {
        role: 'user' as const,
        content: `Generate a thoughtful response to this tweet:\n\n"${tweetContent}"`,
      },
    ];

    return this.streamResponse(messages, context);
  }

  /**
   * Calculate bullish score for a tweet
   */
  async calculateBullishScore(content: string): Promise<number> {
    const model = getModel(this.config.model!);
    
    const result = await streamText({
      model,
      messages: [
        {
          role: 'system',
          content: `You are a sentiment analysis expert. Analyze the sentiment and positivity of tweets and return a "bullish score" from 1-100 where:
          - 1-20: Very negative, bearish, pessimistic
          - 21-40: Somewhat negative, skeptical
          - 41-60: Neutral, mixed sentiment
          - 61-80: Positive, optimistic
          - 81-100: Very positive, bullish, enthusiastic
          
          Respond with ONLY a number between 1-100.`,
        },
        {
          role: 'user',
          content: `Analyze this tweet and give it a bullish score: "${content}"`,
        },
      ],
      maxTokens: 10,
      temperature: 0.3,
    });

    // Extract number from response
    let text = '';
    for await (const chunk of result.textStream) {
      text += chunk;
    }
    
    const score = parseInt(text.trim().replace(/\D/g, ''));
    return Math.max(1, Math.min(100, score || 50)); // Default to 50 if parsing fails
  }

  /**
   * Core streaming response method
   */
  private async streamResponse(
    messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>,
    context: BenjiContext = {}
  ) {
    console.log('🔄 Benji: Starting streamResponse with config:', {
      model: this.config.model,
      enableTools: this.config.enableTools,
      maxTokens: this.config.maxTokens,
      userId: this.config.userId
    });

    try {
      const model = getModel(this.config.model!);
      console.log('✅ Benji: Model loaded successfully');

      const tools = this.config.enableTools ? {
        searchWeb: xaiSearchTool,
        searchKnowledge: exaSearchTool,
        generateImage: imageGenerationTool,
      } : undefined;

      console.log('🛠️ Benji: Tools configured:', this.config.enableTools ? 'Enabled' : 'Disabled');

      const result = streamText({
        model,
        messages,
        tools,
        maxTokens: this.config.maxTokens,
        temperature: this.config.temperature,
        maxSteps: this.config.maxSteps,
      });

      console.log('🚀 Benji: streamText call initiated');
      return result;
    } catch (error) {
      console.error('❌ Benji Agent streamResponse error:', error);
      console.error('🔍 Benji: Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        config: this.config
      });
      throw new Error(`AI generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Build system prompt based on context
   */
  private buildSystemPrompt(type: 'mention' | 'quick-reply', context: BenjiContext): string {
    let prompt = `You are Benji, an AI assistant specialized in social media engagement.

CRITICAL RESPONSE RULES:
- RESPOND ONLY with the direct tweet reply - NO explanations or context
- BE ULTRA-CONCISE - every word must add value
- NEVER include phrases like "This tweet seems to be..." or "Here's a response:"
- ONLY provide content that is directly relevant to the original tweet
- Stay within 280 characters maximum
- ALWAYS respond in FIRST PERSON as the account owner (use "I", "my", "me")
- NEVER refer to the account owner in third person or by name
- NEVER say things like "Elon Musk consistently pushes boundaries" - you ARE the account owner
- Answer as if you are the account owner responding directly to the conversation

Your personality:
- Professional yet personable
- Knowledgeable and helpful
- Always concise and direct
- Respectful and positive

Guidelines:
- Match the tone of the original tweet
- Provide immediate value (insights, questions, resources)
- Be conversational and authentic
- Avoid promotional language
- Use tools only when they add specific value to the response`;

    // Add personality prompt if available
    if (this.config.personalityPrompt) {
      prompt += `\n\nPersonality Style: ${this.config.personalityPrompt}`;
    }

    // Add custom system prompt if available
    if (this.config.customSystemPrompt) {
      prompt += `\n\nAdditional Instructions: ${this.config.customSystemPrompt}`;
    }

    // Always add the first-person instruction
    prompt += `\n\nRemember: You ARE the account owner. Respond in first person (I/my/me) and always answer in short. Never reference yourself in third person.`;

    if (type === 'mention') {
      return prompt + `\n\nRespond to this mention with a direct, engaging reply that represents the monitored account professionally.`;
    }
    
    return prompt + `\n\nGenerate a direct, authentic reply that adds meaningful value to the conversation.`;
  }

}

// Convenience function for quick usage
export function createBenjiAgent(config: BenjiConfig = {}) {
  return new BenjiAgent(config);
}

// Helper function to get agent for user
export async function getBenjiForUser(userId: string) {
  console.log('🤖 Benji: Getting agent for user:', userId);

  // Get user's plan and personality from database
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      plan: true,
      selectedPersonality: true,
    },
  });

  if (!user) {
    console.error('❌ Benji: User not found:', userId);
    throw new Error('User not found');
  }

  console.log('✅ Benji: User found with plan:', user.plan.name);
  console.log('🎭 Benji: Personality:', user.selectedPersonality?.name || 'None');
  console.log('📝 Benji: Custom prompt:', user.customSystemPrompt ? 'Yes' : 'No');

  return new BenjiAgent({
    userId,
    userPlan: user.plan.name,
    enableTools: true,
    personalityPrompt: user.selectedPersonality?.systemPrompt,
    customSystemPrompt: user.customSystemPrompt || undefined,
  });
}